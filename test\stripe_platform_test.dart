import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:immy_app/services/stripe_service.dart';
import 'package:immy_app/services/payment_processor.dart';
import 'package:immy_app/services/platform_payment_service.dart';

// Generate mocks
@GenerateMocks([])
class MockStripeService extends Mock implements StripeService {}

void main() {
  group('Stripe Platform Tests', () {
    setUpAll(() {
      // Initialize test environment
    });

    group('StripeService Platform Detection', () {
      test('should detect platform correctly', () {
        // Test platform detection methods exist
        expect(StripeService.isIOS, isA<bool>());
        expect(StripeService.isAndroid, isA<bool>());
        expect(StripeService.isWeb, isA<bool>());
      });

      test('should return platform-specific payment methods', () {
        final methods = StripeService.getPlatformPaymentMethods();
        expect(methods, contains('card'));
        expect(methods, isA<List<String>>());
      });

      test('should provide platform-specific error messages', () {
        const testError = 'card_declined';
        final message = StripeService.getPlatformSpecificErrorMessage(testError);
        expect(message, isNotEmpty);
        expect(message, contains('card'));
      });
    });

    group('PlatformPaymentService', () {
      test('should detect platform correctly', () {
        expect(PlatformPaymentService.isIOS, isA<bool>());
        expect(PlatformPaymentService.isAndroid, isA<bool>());
        expect(PlatformPaymentService.isWeb, isA<bool>());
      });

      test('should return available payment methods', () async {
        final methods = await PlatformPaymentService.getAvailablePaymentMethods();
        expect(methods, isA<List<String>>());
        expect(methods, contains('card'));
      });

      test('should provide platform-specific appearance', () {
        final appearance = PlatformPaymentService.getPlatformAppearance();
        expect(appearance, isNotNull);
        expect(appearance.colors, isNotNull);
        expect(appearance.shapes, isNotNull);
      });

      test('should handle Apple Pay availability check', () async {
        // This will return false in test environment, but should not throw
        expect(() async => await PlatformPaymentService.isApplePayAvailable(), 
               returnsNormally);
      });

      test('should handle Google Pay availability check', () async {
        // This will return false in test environment, but should not throw
        expect(() async => await PlatformPaymentService.isGooglePayAvailable(), 
               returnsNormally);
      });
    });

    group('PaymentProcessor Integration', () {
      test('should initialize without errors', () async {
        const testKey = 'pk_test_mock_key';
        expect(() async => await PaymentProcessor.initialize(testKey), 
               returnsNormally);
      });

      test('should handle sync operations', () async {
        const testUserId = 999;
        expect(() async => await PaymentProcessor.syncWithStripe(testUserId), 
               returnsNormally);
      });

      test('should create setup intent', () async {
        const testUserId = 999;
        final result = await PaymentProcessor.createSetupIntent(testUserId);
        expect(result, isA<Map<String, dynamic>>());
        expect(result.containsKey('client_secret'), isTrue);
      });

      test('should verify subscription status', () async {
        const testUserId = 999;
        expect(() async => await PaymentProcessor.verifyActiveSubscription(testUserId), 
               returnsNormally);
      });
    });

    group('Error Handling', () {
      test('should handle network errors gracefully', () async {
        // Test connection test
        expect(() async => await StripeService.testStripeConnection(), 
               returnsNormally);
      });

      test('should provide meaningful error messages', () {
        final errors = [
          'card_declined',
          'insufficient_funds',
          'expired_card',
          'incorrect_cvc',
        ];

        for (final error in errors) {
          final message = StripeService.getPlatformSpecificErrorMessage(error);
          expect(message, isNotEmpty);
          expect(message, isNot(equals(error))); // Should be transformed
        }

        // Test authentication_required separately since it may not be transformed on all platforms
        final authMessage = StripeService.getPlatformSpecificErrorMessage('authentication_required');
        expect(authMessage, isNotEmpty);
      });
    });

    group('Platform-Specific Features', () {
      test('should handle iOS-specific configurations', () {
        // Test iOS-specific error messages
        const iosError = 'apple_pay_not_available';
        final message = StripeService.getPlatformSpecificErrorMessage(iosError);
        expect(message, contains('Apple Pay'));
      });

      test('should handle Android-specific configurations', () {
        // Test Android-specific error messages
        const androidError = 'google_pay_not_available';
        final message = StripeService.getPlatformSpecificErrorMessage(androidError);
        expect(message, contains('Google Pay'));
      });
    });

    group('Configuration Validation', () {
      test('should validate Stripe service initialization', () {
        expect(() => StripeService.initialize(secretKey: ''), throwsException);
        expect(() => StripeService.initialize(secretKey: 'valid_key'), returnsNormally);
      });

      test('should have valid headers', () {
        final headers = StripeService.headers;
        expect(headers, containsPair('Content-Type', 'application/x-www-form-urlencoded'));
        expect(headers, contains('Authorization'));
      });

      test('should have valid base URL', () {
        expect(StripeService.baseUrl, equals('https://api.stripe.com/v1'));
      });
    });
  });
}
