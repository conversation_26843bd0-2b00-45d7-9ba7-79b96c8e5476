# Stripe ProGuard rules
-keep class com.stripe.** { *; }
-keep class com.stripe.android.** { *; }
-dontwarn com.stripe.**

# Keep Stripe model classes
-keep class com.stripe.android.model.** { *; }
-keep class com.stripe.android.view.** { *; }

# Keep Flutter Stripe plugin classes
-keep class io.flutter.plugins.stripe.** { *; }
-dontwarn io.flutter.plugins.stripe.**

# Keep JSON serialization for Stripe
-keepattributes Signature
-keepattributes *Annotation*
-keep class * extends com.google.gson.TypeAdapter
-keep class * implements com.google.gson.TypeAdapterFactory
-keep class * implements com.google.gson.JsonSerializer
-keep class * implements com.google.gson.JsonDeserializer

# Keep HTTP client classes used by Stripe
-keep class okhttp3.** { *; }
-keep class retrofit2.** { *; }
-dontwarn okhttp3.**
-dontwarn retrofit2.**

# Keep classes for reflection
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}
