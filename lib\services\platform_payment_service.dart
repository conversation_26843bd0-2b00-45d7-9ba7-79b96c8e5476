import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_stripe/flutter_stripe.dart';
import 'stripe_service.dart';

/// Platform-specific payment service for handling iOS and Android payment methods
class PlatformPaymentService {
  // Platform detection
  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get isWeb => kIsWeb;

  /// Check if Apple Pay is available (iOS only)
  static Future<bool> isApplePayAvailable() async {
    if (!isIOS) return false;

    try {
      // For newer versions of flutter_stripe, we'll use a simpler check
      // This is a placeholder - in production you'd check device capabilities
      return true; // Assume available for now
    } catch (e) {
      if (kDebugMode) {
        print('Error checking Apple Pay availability: $e');
      }
      return false;
    }
  }

  /// Check if Google Pay is available (Android only)
  static Future<bool> isGooglePayAvailable() async {
    if (!isAndroid) return false;

    try {
      // For newer versions of flutter_stripe, we'll use a simpler check
      // This is a placeholder - in production you'd check device capabilities
      return true; // Assume available for now
    } catch (e) {
      if (kDebugMode) {
        print('Error checking Google Pay availability: $e');
      }
      return false;
    }
  }

  /// Get available payment methods for the current platform
  static Future<List<String>> getAvailablePaymentMethods() async {
    final methods = <String>['card'];
    
    if (isIOS && await isApplePayAvailable()) {
      methods.add('apple_pay');
    } else if (isAndroid && await isGooglePayAvailable()) {
      methods.add('google_pay');
    }
    
    return methods;
  }

  /// Present Apple Pay payment sheet (iOS only)
  static Future<Map<String, dynamic>> presentApplePay({
    required String amount,
    required String currency,
    required String merchantDisplayName,
    String? countryCode = 'US',
  }) async {
    if (!isIOS) {
      throw Exception('Apple Pay is only available on iOS');
    }

    try {
      // For now, we'll return a placeholder response
      // In production, you'd implement actual Apple Pay integration
      return {
        'success': false,
        'error': 'Apple Pay integration requires additional setup',
        'method': 'apple_pay'
      };
    } catch (e) {
      return {
        'success': false,
        'error': StripeService.getPlatformSpecificErrorMessage(e.toString()),
        'method': 'apple_pay'
      };
    }
  }

  /// Present Google Pay payment sheet (Android only)
  static Future<Map<String, dynamic>> presentGooglePay({
    required String amount,
    required String currency,
    required String merchantDisplayName,
    String? countryCode = 'US',
  }) async {
    if (!isAndroid) {
      throw Exception('Google Pay is only available on Android');
    }

    try {
      // For now, we'll return a placeholder response
      // In production, you'd implement actual Google Pay integration
      return {
        'success': false,
        'error': 'Google Pay integration requires additional setup',
        'method': 'google_pay'
      };
    } catch (e) {
      return {
        'success': false,
        'error': StripeService.getPlatformSpecificErrorMessage(e.toString()),
        'method': 'google_pay'
      };
    }
  }

  /// Get platform-specific payment sheet appearance
  static PaymentSheetAppearance getPlatformAppearance() {
    if (isIOS) {
      return const PaymentSheetAppearance(
        colors: PaymentSheetAppearanceColors(
          primary: Colors.purple,
          background: Colors.white,
          componentBackground: Color(0xFFF7F7F7),
          componentBorder: Color(0xFFE1E1E1),
          componentDivider: Color(0xFFE1E1E1),
          primaryText: Colors.black,
          secondaryText: Color(0xFF6B7280),
          componentText: Colors.black,
          placeholderText: Color(0xFF9CA3AF),
        ),
        shapes: PaymentSheetShape(
          borderRadius: 12,
          borderWidth: 1,
        ),
        primaryButton: PaymentSheetPrimaryButtonAppearance(
          colors: PaymentSheetPrimaryButtonTheme(
            light: PaymentSheetPrimaryButtonThemeColors(
              background: Colors.purple,
              text: Colors.white,
              border: Colors.purple,
            ),
          ),
        ),
      );
    } else {
      // Android Material Design appearance
      return const PaymentSheetAppearance(
        colors: PaymentSheetAppearanceColors(
          primary: Colors.purple,
          background: Colors.white,
          componentBackground: Colors.white,
          componentBorder: Color(0xFFE0E0E0),
          componentDivider: Color(0xFFE0E0E0),
          primaryText: Colors.black87,
          secondaryText: Color(0xFF757575),
          componentText: Colors.black87,
          placeholderText: Color(0xFF9E9E9E),
        ),
        shapes: PaymentSheetShape(
          borderRadius: 8,
          borderWidth: 1,
        ),
        primaryButton: PaymentSheetPrimaryButtonAppearance(
          colors: PaymentSheetPrimaryButtonTheme(
            light: PaymentSheetPrimaryButtonThemeColors(
              background: Colors.purple,
              text: Colors.white,
              border: Colors.purple,
            ),
          ),
        ),
      );
    }
  }

  /// Initialize payment sheet with platform-specific settings
  static Future<void> initializePlatformPaymentSheet({
    String? paymentIntentClientSecret,
    String? setupIntentClientSecret,
    required String merchantDisplayName,
    String? customerId,
  }) async {
    final appearance = getPlatformAppearance();
    
    if (paymentIntentClientSecret != null) {
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          paymentIntentClientSecret: paymentIntentClientSecret,
          merchantDisplayName: merchantDisplayName,
          customerId: customerId,
          style: isIOS ? ThemeMode.light : ThemeMode.system,
          appearance: appearance,
          billingDetailsCollectionConfiguration: const BillingDetailsCollectionConfiguration(
            name: CollectionMode.always,
            email: CollectionMode.always,
          ),
          allowsDelayedPaymentMethods: true,
        ),
      );
    } else if (setupIntentClientSecret != null) {
      await Stripe.instance.initPaymentSheet(
        paymentSheetParameters: SetupPaymentSheetParameters(
          setupIntentClientSecret: setupIntentClientSecret,
          merchantDisplayName: merchantDisplayName,
          customerId: customerId,
          style: isIOS ? ThemeMode.light : ThemeMode.system,
          appearance: appearance,
        ),
      );
    }
  }

  /// Present payment sheet with platform-specific handling
  static Future<Map<String, dynamic>> presentPaymentSheet() async {
    try {
      await Stripe.instance.presentPaymentSheet();
      return {'success': true};
    } catch (e) {
      return {
        'success': false,
        'error': StripeService.getPlatformSpecificErrorMessage(e.toString()),
      };
    }
  }

  /// Get platform-specific error message for payment failures
  static String getPaymentErrorMessage(String error) {
    return StripeService.getPlatformSpecificErrorMessage(error);
  }
}
