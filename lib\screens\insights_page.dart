import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:collection/collection.dart';
import '../services/api_service.dart';
import '../services/history_service.dart';
import '../services/payment_processor.dart';
import 'insights_history_page.dart';
import 'no_api_key_screen.dart';
import 'no_subscription_screen.dart';

class InsightsPage extends StatefulWidget {
  final ApiService apiService;
  final int userId;
  
  const InsightsPage({Key? key, required this.apiService, required this.userId}) : super(key: key);

  @override
  _InsightsPageState createState() => _InsightsPageState();
}

class _InsightsPageState extends State<InsightsPage> {
  bool _isLoading = false;
  Map<String, dynamic>? _insightsData;
  String? _errorMessage;
  bool _hasApiKey = false;
  bool _hasActiveSubscription = false;
  final HistoryService _historyService = HistoryService();

  @override
  void initState() {
    super.initState();
    _checkApiKeyAndLoadData();
  }

  bool _isLargeScreen(BuildContext context) {
    return MediaQuery.of(context).size.width >= 768;
  }

  EdgeInsets _getResponsivePadding(BuildContext context) {
    if (_isLargeScreen(context)) {
      return const EdgeInsets.symmetric(horizontal: 32, vertical: 24);
    }
    return const EdgeInsets.all(12);
  }

  double? _getCardMaxWidth(BuildContext context) {
    if (_isLargeScreen(context)) {
      return 1000;
    }
    return null;
  }

  String _getCurrentTimeSlot() {
    final now = DateTime.now();
    return now.hour < 12 ? 'AM' : 'PM';
  }

  Future<void> _checkApiKeyAndLoadData() async {
    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final prefs = await SharedPreferences.getInstance();
      final apiKey = prefs.getString('user_api_key');

      if (apiKey == null || apiKey.isEmpty) {
        setState(() {
          _hasApiKey = false;
          _isLoading = false;
        });
        return;
      }

      setState(() {
        _hasApiKey = true;
      });

      // Check subscription status
      final hasActiveSubscription = await PaymentProcessor.verifyActiveSubscription(widget.userId);
      setState(() {
        _hasActiveSubscription = hasActiveSubscription;
      });

      if (!hasActiveSubscription) {
        setState(() {
          _isLoading = false;
        });
        return;
      }

      await _loadInsights();
    } catch (e) {
      setState(() {
        _errorMessage = 'Error loading developmental insights: $e';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadInsights() async {
    if (!_hasApiKey || !_hasActiveSubscription) return;

    setState(() {
      _isLoading = true;
      _errorMessage = null;
    });

    try {
      final currentSlot = _getCurrentTimeSlot();
      final prefs = await SharedPreferences.getInstance();
      final cachedDataString = prefs.getString('insights_cached_data');
      final lastSlot = prefs.getString('insights_last_slot');

      if (currentSlot == lastSlot && cachedDataString != null) {
        final cachedData = json.decode(cachedDataString) as Map<String, dynamic>;
        setState(() => _insightsData = cachedData);
      } else {
        final insights = await widget.apiService.getInsights();

        if (cachedDataString != null) {
          final cachedData = json.decode(cachedDataString) as Map<String, dynamic>;
          final isEqual = const DeepCollectionEquality().equals(insights, cachedData);

          if (!isEqual) {
            await _historyService.saveInsightsHistory(insights);
          }
        } else {
          await _historyService.saveInsightsHistory(insights);
        }

        await prefs.setString('insights_cached_data', json.encode(insights));
        await prefs.setString('insights_last_slot', currentSlot);

        setState(() => _insightsData = insights);
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Failed to load developmental insights: $e';
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _logout() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('user_api_key');
    
    if (mounted) {
      Navigator.of(context).pushReplacementNamed('/login');
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!_hasApiKey && !_isLoading) {
      return NoApiKeyScreen(onLogout: _logout);
    }

    if (_hasApiKey && !_hasActiveSubscription && !_isLoading) {
      return NoSubscriptionScreen(onLogout: _logout, userId: widget.userId);
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Development Insights', style: TextStyle(fontWeight: FontWeight.bold)),
        backgroundColor: const Color(0xFF8B5CF6),
        foregroundColor: Colors.white,
        elevation: 0,
        centerTitle: true,
        actions: [
          IconButton(
            icon: const Icon(Icons.history),
            tooltip: 'View History',
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => InsightsHistoryPage(historyService: _historyService),
                ),
              );
            },
          ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(color: Color(0xFF8B5CF6)),
            SizedBox(height: 16),
            Text("Analyzing your child's development...", 
              style: TextStyle(color: Color(0xFF6B7280), fontWeight: FontWeight.w500))
          ],
        )
      );
    }
    
    if (_errorMessage != null) return _buildErrorView();
    
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [const Color(0xFFF3E8FF), Colors.white],
        ),
      ),
      child: SafeArea(
        child: SingleChildScrollView(
          padding: _getResponsivePadding(context),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildExecutiveSummary(),
              const SizedBox(height: 20),
              _buildDevelopmentalSnapshot(),
              const SizedBox(height: 16),
              _buildAcademicAssessment(),
              const SizedBox(height: 16),
              _buildSocialEmotionalInsights(),
              const SizedBox(height: 16),
              _buildCommunicationProfile(),
              const SizedBox(height: 16),
              _buildDevelopmentalRecommendations(),
              const SizedBox(height: 60),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildExecutiveSummary() {
    final isLarge = _isLargeScreen(context);
    final executiveSummary = _insightsData?['executive_summary'] ?? '';
    
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 16 : 12),
                  decoration: BoxDecoration(
                    color: const Color(0xFFE0E7FF),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    Icons.insights, 
                    color: const Color(0xFF4F46E5), 
                    size: isLarge ? 32 : 28
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Development Overview",
                        style: TextStyle(
                          fontSize: isLarge ? 20 : 18,
                          fontWeight: FontWeight.bold,
                          color: const Color(0xFF374151),
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        "Key insights about your child's growth",
                        style: TextStyle(fontSize: isLarge ? 14 : 12, color: const Color(0xFF6B7280)),
                      ),
                    ],
                  ),
                ),
              ],
            ),
            if (executiveSummary.isNotEmpty) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: const Color(0xFFF9FAFB),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: Colors.grey.shade200),
                ),
                child: Text(
                  executiveSummary,
                  style: TextStyle(
                    fontSize: isLarge ? 15 : 14,
                    color: const Color(0xFF4B5563),
                    height: 1.5,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildDevelopmentalSnapshot() {
    final detailedInsights = _insightsData?['detailed_insights'] as Map<String, dynamic>?;
    final executiveOverview = detailedInsights?['executive_overview'] as Map<String, dynamic>?;
    if (executiveOverview == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    final keyFindings = executiveOverview['key_findings_summary'] ?? '';
    final developmentalSnapshot = executiveOverview['overall_developmental_snapshot'] ?? '';
    final priorityRecommendations = executiveOverview['top_priority_recommendations'] as List? ?? [];

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFDCFCE7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.child_care,
                    color: const Color(0xFF16A34A),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Developmental Snapshot",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (keyFindings.isNotEmpty) ...[
              _buildInsightSection("Key Findings", keyFindings, isLarge),
              const SizedBox(height: 12),
            ],
            if (developmentalSnapshot.isNotEmpty) ...[
              _buildInsightSection("Overall Development", developmentalSnapshot, isLarge),
              const SizedBox(height: 12),
            ],
            if (priorityRecommendations.isNotEmpty) ...[
              Text(
                "Priority Recommendations:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 14,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF374151),
                ),
              ),
              const SizedBox(height: 8),
              ...priorityRecommendations.map((recommendation) => 
                Padding(
                  padding: const EdgeInsets.only(bottom: 6),
                  child: Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Icon(Icons.arrow_right, size: 16, color: const Color(0xFF16A34A)),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          recommendation.toString(),
                          style: TextStyle(
                            fontSize: isLarge ? 14 : 13,
                            color: const Color(0xFF4B5563),
                            height: 1.4,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildAcademicAssessment() {
    final detailedInsights = _insightsData?['detailed_insights'] as Map<String, dynamic>?;
    final academicAssessment = detailedInsights?['academic_and_cognitive_assessment'] as Map<String, dynamic>?;
    if (academicAssessment == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    final subjectEngagement = academicAssessment['subject_matter_engagement'] as List? ?? [];
    final learningPatterns = academicAssessment['learning_patterns_and_preferences'] as Map<String, dynamic>?;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFED7AA),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.school,
                    color: const Color(0xFFEA580C),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Academic & Learning",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            ...subjectEngagement.map((subject) => _buildSubjectCard(subject, isLarge)),
            if (learningPatterns != null) ...[
              const SizedBox(height: 12),
              _buildLearningPatternsCard(learningPatterns, isLarge),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSubjectCard(dynamic subject, bool isLarge) {
    if (subject is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subject['subject_area']?.toString() ?? 'Subject Area',
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF374151),
            ),
          ),
          if (subject['observed_understanding_level'] != null) ...[
            const SizedBox(height: 8),
            Row(
              children: [
                Icon(Icons.trending_up, size: 16, color: const Color(0xFFEA580C)),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    "Level: ${subject['observed_understanding_level']}",
                    style: TextStyle(
                      fontSize: isLarge ? 14 : 13,
                      color: const Color(0xFF4B5563),
                    ),
                  ),
                ),
              ],
            ),
          ],
          if (subject['engagement_level'] != null) ...[
            const SizedBox(height: 6),
            Row(
              children: [
                Icon(Icons.favorite, size: 16, color: const Color(0xFFDB2777)),
                const SizedBox(width: 6),
                Expanded(
                  child: Text(
                    "Engagement: ${subject['engagement_level']}",
                    style: TextStyle(
                      fontSize: isLarge ? 14 : 13,
                      color: const Color(0xFF4B5563),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildLearningPatternsCard(Map<String, dynamic> patterns, bool isLarge) {
    return Container(
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "Learning Patterns",
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF374151),
            ),
          ),
          if (patterns['academic_strengths'] != null) ...[
            const SizedBox(height: 12),
            Text(
              "Strengths:",
              style: TextStyle(
                fontSize: isLarge ? 14 : 13,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF374151),
              ),
            ),
            const SizedBox(height: 4),
            ...((patterns['academic_strengths'] as List?) ?? []).map((strength) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 4),
                child: Row(
                  children: [
                    Icon(Icons.star, size: 14, color: const Color(0xFFF59E0B)),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        strength.toString(),
                        style: TextStyle(
                          fontSize: isLarge ? 13 : 12,
                          color: const Color(0xFF4B5563),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSocialEmotionalInsights() {
    final detailedInsights = _insightsData?['detailed_insights'] as Map<String, dynamic>?;
    final socialEmotional = detailedInsights?['social_emotional_and_behavioral_insights'] as Map<String, dynamic>?;
    if (socialEmotional == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    final emotionalIntelligence = socialEmotional['emotional_intelligence_and_regulation'] as Map<String, dynamic>?;
    final socialInteraction = socialEmotional['social_interaction_and_skills'] as Map<String, dynamic>?;
    final behavioralPatterns = socialEmotional['behavioral_patterns'] as Map<String, dynamic>?;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFFCE7F3),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.favorite,
                    color: const Color(0xFFDB2777),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Social & Emotional",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (emotionalIntelligence != null) ...[
              _buildEmotionalCard("Emotional Development", emotionalIntelligence, isLarge),
              const SizedBox(height: 12),
            ],
            if (socialInteraction != null) ...[
              _buildEmotionalCard("Social Skills", socialInteraction, isLarge),
              const SizedBox(height: 12),
            ],
            if (behavioralPatterns != null) ...[
              _buildEmotionalCard("Behavioral Patterns", behavioralPatterns, isLarge),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildEmotionalCard(String title, Map<String, dynamic> data, bool isLarge) {
    return Container(
      padding: EdgeInsets.all(isLarge ? 16 : 14),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            style: TextStyle(
              fontSize: isLarge ? 16 : 15,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF374151),
            ),
          ),
          const SizedBox(height: 8),
          ...data.entries.where((entry) => entry.key != 'evidence_from_conversation' && entry.key != 'assessment_confidence_level').map((entry) => 
            Padding(
              padding: const EdgeInsets.only(bottom: 6),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _formatTitle(entry.key),
                    style: TextStyle(
                      fontSize: isLarge ? 13 : 12,
                      fontWeight: FontWeight.w500,
                      color: const Color(0xFF6B7280),
                    ),
                  ),
                  const SizedBox(height: 2),
                  Text(
                    entry.value.toString(),
                    style: TextStyle(
                      fontSize: isLarge ? 14 : 13,
                      color: const Color(0xFF4B5563),
                      height: 1.3,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommunicationProfile() {
    final detailedInsights = _insightsData?['detailed_insights'] as Map<String, dynamic>?;
    final communicationProfile = detailedInsights?['language_and_communication_profile'] as Map<String, dynamic>?;
    if (communicationProfile == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFDBEAFE),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    color: const Color(0xFF2563EB),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Communication Profile",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              padding: EdgeInsets.all(isLarge ? 16 : 14),
              decoration: BoxDecoration(
                color: const Color(0xFFF9FAFB),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: communicationProfile.entries.where((entry) => entry.key != 'evidence_from_conversation' && entry.key != 'assessment_confidence_level').map((entry) => 
                  Padding(
                    padding: const EdgeInsets.only(bottom: 12),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatTitle(entry.key),
                          style: TextStyle(
                            fontSize: isLarge ? 15 : 14,
                            fontWeight: FontWeight.w600,
                            color: const Color(0xFF374151),
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          entry.value.toString(),
                          style: TextStyle(
                            fontSize: isLarge ? 14 : 13,
                            color: const Color(0xFF4B5563),
                            height: 1.4,
                          ),
                        ),
                      ],
                    ),
                  ),
                ).toList(),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDevelopmentalRecommendations() {
    final detailedInsights = _insightsData?['detailed_insights'] as Map<String, dynamic>?;
    final recommendations = detailedInsights?['developmental_recommendations_and_strategies'] as Map<String, dynamic>?;
    if (recommendations == null) return const SizedBox.shrink();

    final isLarge = _isLargeScreen(context);
    final priorityAreas = recommendations['priority_areas_for_development'] as List? ?? [];
    final shortTermGoals = recommendations['actionable_goals_short_term'] as List? ?? [];
    final supportStrategies = recommendations['support_strategies_for_stakeholders'] as Map<String, dynamic>?;

    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: EdgeInsets.all(isLarge ? 20 : 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: EdgeInsets.all(isLarge ? 12 : 10),
                  decoration: BoxDecoration(
                    color: const Color(0xFFDCFCE7),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.trending_up,
                    color: const Color(0xFF16A34A),
                    size: isLarge ? 24 : 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  "Development Recommendations",
                  style: TextStyle(
                    fontSize: isLarge ? 18 : 16,
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF374151),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (priorityAreas.isNotEmpty) ...[
              Text(
                "Priority Focus Areas:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF374151),
                ),
              ),
              const SizedBox(height: 8),
              ...priorityAreas.map((area) => _buildPriorityAreaCard(area, isLarge)),
              const SizedBox(height: 16),
            ],
            if (shortTermGoals.isNotEmpty) ...[
              Text(
                "Short-term Goals:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF374151),
                ),
              ),
              const SizedBox(height: 8),
              ...shortTermGoals.map((goal) => _buildGoalCard(goal, isLarge)),
              const SizedBox(height: 16),
            ],
            if (supportStrategies?['for_parents'] != null) ...[
              Text(
                "Tips for Parents:",
                style: TextStyle(
                  fontSize: isLarge ? 16 : 15,
                  fontWeight: FontWeight.w600,
                  color: const Color(0xFF374151),
                ),
              ),
              const SizedBox(height: 8),
              ...((supportStrategies!['for_parents'] as List?) ?? []).map((strategy) => 
                _buildParentStrategyCard(strategy, isLarge)
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildPriorityAreaCard(dynamic area, bool isLarge) {
    if (area is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(isLarge ? 14 : 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            area['area']?.toString() ?? 'Development Area',
            style: TextStyle(
              fontSize: isLarge ? 15 : 14,
              fontWeight: FontWeight.w600,
              color: const Color(0xFF374151),
            ),
          ),
          if (area['rationale'] != null) ...[
            const SizedBox(height: 6),
            Text(
              area['rationale'].toString(),
              style: TextStyle(
                fontSize: isLarge ? 13 : 12,
                color: const Color(0xFF4B5563),
                height: 1.3,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildGoalCard(dynamic goal, bool isLarge) {
    if (goal is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(isLarge ? 14 : 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.flag, size: 16, color: const Color(0xFF16A34A)),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  goal['goal']?.toString() ?? 'Goal',
                  style: TextStyle(
                    fontSize: isLarge ? 15 : 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF374151),
                  ),
                ),
              ),
            ],
          ),
          if (goal['suggested_activities_or_interventions'] != null) ...[
            const SizedBox(height: 8),
            Text(
              "Activities:",
              style: TextStyle(
                fontSize: isLarge ? 13 : 12,
                fontWeight: FontWeight.w500,
                color: const Color(0xFF6B7280),
              ),
            ),
            const SizedBox(height: 4),
            ...((goal['suggested_activities_or_interventions'] as List?) ?? []).map((activity) => 
              Padding(
                padding: const EdgeInsets.only(bottom: 2),
                child: Row(
                  children: [
                    Icon(Icons.play_circle_outline, size: 12, color: const Color(0xFF16A34A)),
                    const SizedBox(width: 6),
                    Expanded(
                      child: Text(
                        activity.toString(),
                        style: TextStyle(
                          fontSize: isLarge ? 13 : 12,
                          color: const Color(0xFF4B5563),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildParentStrategyCard(dynamic strategy, bool isLarge) {
    if (strategy is! Map<String, dynamic>) return const SizedBox.shrink();
    
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: EdgeInsets.all(isLarge ? 14 : 12),
      decoration: BoxDecoration(
        color: const Color(0xFFF9FAFB),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade200),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.home, size: 16, color: const Color(0xFF16A34A)),
              const SizedBox(width: 6),
              Expanded(
                child: Text(
                  strategy['strategy_type']?.toString() ?? 'Strategy',
                  style: TextStyle(
                    fontSize: isLarge ? 15 : 14,
                    fontWeight: FontWeight.w600,
                    color: const Color(0xFF374151),
                  ),
                ),
              ),
            ],
          ),
          if (strategy['description_and_examples'] != null) ...[
            const SizedBox(height: 6),
            Text(
              strategy['description_and_examples'].toString(),
              style: TextStyle(
                fontSize: isLarge ? 13 : 12,
                color: const Color(0xFF4B5563),
                height: 1.3,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildInsightSection(String title, String content, bool isLarge) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          "$title:",
          style: TextStyle(
            fontSize: isLarge ? 15 : 14,
            fontWeight: FontWeight.w600,
            color: const Color(0xFF374151),
          ),
        ),
        const SizedBox(height: 6),
        Container(
          padding: EdgeInsets.all(isLarge ? 14 : 12),
          decoration: BoxDecoration(
            color: const Color(0xFFF9FAFB),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(color: Colors.grey.shade200),
          ),
          child: Text(
            content,
            style: TextStyle(
              fontSize: isLarge ? 14 : 13,
              color: const Color(0xFF4B5563),
              height: 1.4,
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Container(
        constraints: BoxConstraints(maxWidth: _getCardMaxWidth(context) ?? double.infinity),
        margin: _getResponsivePadding(context),
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: const Color(0xFFFEF2F2),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.05),
              blurRadius: 10,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              padding: const EdgeInsets.all(14),
              decoration: BoxDecoration(
                color: const Color(0xFFFEE2E2),
                borderRadius: BorderRadius.circular(50),
              ),
              child: const Icon(Icons.error_outline, color: Color(0xFFEF4444), size: 40),
            ),
            const SizedBox(height: 20),
            Text(
              _errorMessage ?? 'Something went wrong',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFFB91C1C),
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
            const SizedBox(height: 20),
            Text(
              'Please try again later or contact support if the issue persists.',
              textAlign: TextAlign.center,
              style: const TextStyle(
                color: Color(0xFF6B7280),
                fontSize: 12,
                fontWeight: FontWeight.w400,
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatTitle(String text) {
    if (text.isEmpty) return '';
    
    String formatted = text.replaceAll('_', ' ');
    formatted = formatted.split(' ').map((word) {
      if (word.isEmpty) return word;
      return word[0].toUpperCase() + word.substring(1).toLowerCase();
    }).join(' ');
    
    return formatted;
  }
}