import 'dart:convert';
import 'dart:io';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;
import 'package:immy_app/services/stripe_sync_service.dart';
import 'backend_api_service.dart';
import 'package:flutter_stripe/flutter_stripe.dart';

class StripeService {
  static const String _defaultSecretKey = 'sk_test_51R00wJP1l4vbhTn5Xfe5zWNZrVtHyA7EeP1REpL92RXarOtVRelDEPPHBNdvEdhWRFMd66CWmOLd2cCI2ZF6aAls00jM2x0sdT';
  static const String baseUrl = 'https://api.stripe.com/v1';

  static String? _overrideSecretKey;

  // Platform detection
  static bool get isIOS => !kIsWeb && Platform.isIOS;
  static bool get isAndroid => !kIsWeb && Platform.isAndroid;
  static bool get isWeb => kIsWeb;

  static void initialize({
    required String secretKey,
    String? publishableKey,
    bool testMode = false, // for compatibility
  }) {
    if (secretKey.isEmpty) throw Exception('Stripe secret key cannot be empty');
    _overrideSecretKey = secretKey;
    print('[StripeService] Initialized');
  }

  static Map<String, String> get headers => {
        'Authorization': 'Bearer ${_overrideSecretKey ?? _defaultSecretKey}',
        'Content-Type': 'application/x-www-form-urlencoded',
      };

  // ========== CUSTOMER ==========
  static Future<Map<String, dynamic>> createCustomer({
    required String email,
    String? name,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/customers'),
      headers: headers,
      body: {
        'email': email,
        if (name != null) 'name': name,
      },
    );
    return _handleResponse(response);
  }

  static Future<String> getOrCreateCustomerId(int userId) async {
    try {
      // Get user information from database
      final userRows = await BackendApiService.executeQuery(
        'SELECT id, email, name, stripe_customer_id FROM Users WHERE id = ?',
        [userId],
      );
      
      if (userRows.isEmpty) {
        throw Exception('User not found');
      }

      final user = userRows.first;
      final currentCustomerId = user['stripe_customer_id'];
      final userEmail = user['email'];
      final userName = user['name'];
      
      if (userEmail == null || userEmail.toString().isEmpty) {
        throw Exception('User email not found');
      }

      // If user already has a customer ID in our database, use it
      if (currentCustomerId != null && currentCustomerId.toString().isNotEmpty) {
        print('Using existing Stripe customer ID: $currentCustomerId for user: $userEmail');
        return currentCustomerId;
      }

      print('Creating new Stripe customer for user: $userEmail');
      
      // Check if a customer with this email already exists in Stripe
      final existingCustomerResponse = await http.get(
        Uri.parse('$baseUrl/customers?email=${Uri.encodeComponent(userEmail)}&limit=1'),
        headers: headers,
      );
      
      final existingCustomerResult = _handleResponse(existingCustomerResponse);
      
      // If customer exists in Stripe but not linked in our database, use that customer
      if (existingCustomerResult['data'] != null && existingCustomerResult['data'].isNotEmpty) {
        final existingCustomerId = existingCustomerResult['data'][0]['id'];
        
        // Update user with the existing customer ID
        await BackendApiService.executeQuery(
          'UPDATE Users SET stripe_customer_id = ? WHERE id = ?',
          [existingCustomerId, userId],
        );
        
        print('Found existing Stripe customer: $existingCustomerId for email: $userEmail');
        return existingCustomerId;
      }
      
      // Create a new customer in Stripe
      final newCustomer = await createCustomer(
        email: userEmail,
        name: userName,
      );

      final newCustomerId = newCustomer['id'];
      print('Created new Stripe customer: $newCustomerId for user: $userEmail');

      // Update user record with new customer ID
      await BackendApiService.executeQuery(
        'UPDATE Users SET stripe_customer_id = ? WHERE id = ?',
        [newCustomerId, userId],
      );

      return newCustomerId;
    } catch (e) {
      print('Error in getOrCreateCustomerId: $e');
      throw Exception('Failed to get or create Stripe customer: $e');
    }
  }

  // ========== PAYMENT INTENT ==========
  static Future<Map<String, dynamic>> createPaymentIntent({
    required String amount,
    required String currency,
    String? customerId,
    String? paymentMethodId,
    Map<String, String>? metadata,
  }) async {
    final body = {
      'amount': amount,
      'currency': currency,
      'payment_method_types[]': 'card',
      'confirmation_method': 'automatic',
      'confirm': 'false',
      'description': 'Immy App Subscription',
      'metadata[integration_check]': 'accept_a_payment',
    };

    if (customerId != null) body['customer'] = customerId;
    if (paymentMethodId != null) body['payment_method'] = paymentMethodId;
    
    // Add metadata if provided
    if (metadata != null) {
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value;
      });
    }

    final response = await http.post(
      Uri.parse('$baseUrl/payment_intents'),
      headers: headers,
      body: body,
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> confirmPaymentIntent({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/payment_intents/$paymentIntentId/confirm'),
      headers: headers,
      body: {
        'payment_method': paymentMethodId,
      },
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> retrievePaymentIntent(String paymentIntentId) async {
    final response = await http.get(
      Uri.parse('$baseUrl/payment_intents/$paymentIntentId'),
      headers: headers,
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> updatePaymentIntent({
    required String paymentIntentId,
    String? paymentMethodId,
    Map<String, String>? metadata,
  }) async {
    final body = <String, String>{};
    if (paymentMethodId != null) body['payment_method'] = paymentMethodId;
    
    // Add metadata if provided
    if (metadata != null) {
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value;
      });
    }

    final response = await http.post(
      Uri.parse('$baseUrl/payment_intents/$paymentIntentId'),
      headers: headers,
      body: body,
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> retryPaymentIntent({
    required String paymentIntentId,
    required String paymentMethodId,
  }) async {
    // First update the payment intent with the new payment method
    await updatePaymentIntent(
      paymentIntentId: paymentIntentId,
      paymentMethodId: paymentMethodId,
    );
    
    // Then confirm the payment intent
    return await confirmPaymentIntent(
      paymentIntentId: paymentIntentId,
      paymentMethodId: paymentMethodId,
    );
  }

  // ========== SETUP INTENT ==========
  static Future<Map<String, dynamic>> createSetupIntent({
    required int userId,
    String? customerId,
  }) async {
    customerId ??= await getOrCreateCustomerId(userId);

    final response = await http.post(
      Uri.parse('$baseUrl/setup_intents'),
      headers: headers,
      body: {
        'customer': customerId,
        'payment_method_types[]': 'card',
        'usage': 'off_session',
      },
    );

    final result = _handleResponse(response);
    return {
      'client_secret': result['client_secret'],
      'setup_intent_id': result['id'],
    };
  }

  // ========== PAYMENT METHOD ==========
  static Future<Map<String, dynamic>> attachPaymentMethodToCustomer({
    required String paymentMethodId,
    required String customerId,
  }) async {
    final response = await http.post(
      Uri.parse('$baseUrl/payment_methods/$paymentMethodId/attach'),
      headers: headers,
      body: {
        'customer': customerId,
      },
    );
    return _handleResponse(response);
  }

  static Future<List<Map<String, dynamic>>> getCustomerPaymentMethods({
    required String customerId,
    String type = 'card',
  }) async {
    final response = await http.get(
      Uri.parse('$baseUrl/payment_methods?customer=$customerId&type=$type'),
      headers: headers,
    );
    final result = _handleResponse(response);
    return List<Map<String, dynamic>>.from(result['data']);
  }

  // ========== SUBSCRIPTION ==========
  static Future<Map<String, dynamic>> createSubscription({
    required String customerId,
    required String priceId,
    String? paymentMethodId,
    Map<String, String>? metadata,
  }) async {
    final body = {
      'customer': customerId,
      'items[0][price]': priceId,
    };
    
    if (paymentMethodId != null) {
      body['default_payment_method'] = paymentMethodId;
    }
    
    // Add metadata if provided
    if (metadata != null) {
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value;
      });
    }

    final response = await http.post(
      Uri.parse('$baseUrl/subscriptions'),
      headers: headers,
      body: body,
    );
    return _handleResponse(response);
  }

  static Future<Map<String, dynamic>> cancelSubscription({
    required String subscriptionId,
  }) async {
    final response = await http.delete(
      Uri.parse('$baseUrl/subscriptions/$subscriptionId'),
      headers: headers,
    );
    return _handleResponse(response);
  }

  // ========== PLATFORM-SPECIFIC METHODS ==========

  /// Get platform-specific payment method types
  static List<String> getPlatformPaymentMethods() {
    final methods = ['card'];

    if (isIOS) {
      // Apple Pay is available on iOS
      methods.add('apple_pay');
    } else if (isAndroid) {
      // Google Pay is available on Android
      methods.add('google_pay');
    }

    return methods;
  }

  /// Create payment intent with platform-specific configurations
  static Future<Map<String, dynamic>> createPlatformOptimizedPaymentIntent({
    required String amount,
    required String currency,
    String? customerId,
    String? paymentMethodId,
    Map<String, String>? metadata,
  }) async {
    final body = {
      'amount': amount,
      'currency': currency,
      'confirmation_method': 'automatic',
      'confirm': 'false',
      'description': 'Immy App Subscription',
      'metadata[integration_check]': 'accept_a_payment',
    };

    // Add platform-specific payment method types
    final paymentMethods = getPlatformPaymentMethods();
    for (int i = 0; i < paymentMethods.length; i++) {
      body['payment_method_types[$i]'] = paymentMethods[i];
    }

    if (customerId != null) body['customer'] = customerId;
    if (paymentMethodId != null) body['payment_method'] = paymentMethodId;

    // Add metadata if provided
    if (metadata != null) {
      metadata.forEach((key, value) {
        body['metadata[$key]'] = value;
      });
    }

    final response = await http.post(
      Uri.parse('$baseUrl/payment_intents'),
      headers: headers,
      body: body,
    );
    return _handleResponse(response);
  }

  /// Handle platform-specific errors
  static String getPlatformSpecificErrorMessage(String error) {
    if (isIOS) {
      // iOS-specific error handling
      if (error.contains('authentication_required')) {
        return 'Please authenticate with Touch ID, Face ID, or your passcode to complete the payment.';
      } else if (error.contains('apple_pay_not_available')) {
        return 'Apple Pay is not available on this device. Please use a card instead.';
      }
    } else if (isAndroid) {
      // Android-specific error handling
      if (error.contains('authentication_required')) {
        return 'Please authenticate with your fingerprint, face unlock, or PIN to complete the payment.';
      } else if (error.contains('google_pay_not_available')) {
        return 'Google Pay is not available on this device. Please use a card instead.';
      }
    }

    // Handle specific platform pay errors
    if (error.contains('apple_pay_not_available')) {
      return 'Apple Pay is not available on this device. Please use a card instead.';
    } else if (error.contains('google_pay_not_available')) {
      return 'Google Pay is not available on this device. Please use a card instead.';
    }

    // Generic error messages
    if (error.contains('card_declined')) {
      return 'Your card was declined. Please try a different payment method.';
    } else if (error.contains('insufficient_funds')) {
      return 'Insufficient funds. Please check your account balance or try a different card.';
    } else if (error.contains('expired_card')) {
      return 'Your card has expired. Please use a different payment method.';
    } else if (error.contains('incorrect_cvc')) {
      return 'The security code (CVC) is incorrect. Please check and try again.';
    }

    return error;
  }

  // ========== HEALTH CHECK ==========
  static Future<bool> testStripeConnection() async {
    try {
      final response = await http.get(
        Uri.parse('$baseUrl/customers?limit=1'),
        headers: headers,
      );
      return response.statusCode == 200;
    } catch (e) {
      if (kDebugMode) {
        print('Stripe connection test failed: $e');
      }
      return false;
    }
  }

  // ========== COMMON ==========
  static Map<String, dynamic> _handleResponse(http.Response response) {
    if (response.statusCode >= 200 && response.statusCode < 300) {
      return jsonDecode(response.body);
    } else {
      print('Stripe error: ${response.body}');
      throw Exception('Stripe API Error: ${response.statusCode} - ${response.body}');
    }
  }

  // Add this new method to check payments in Stripe
  static Future<List<Map<String, dynamic>>> getCustomerPayments(String customerId) async {
    try {
      // Get payment intents for this customer
      final response = await http.get(
        Uri.parse('$baseUrl/payment_intents?customer=$customerId&limit=10'),
        headers: headers,
      );
      final result = _handleResponse(response);
      return List<Map<String, dynamic>>.from(result['data']);
    } catch (e) {
      print('Error fetching customer payments from Stripe: $e');
      return [];
    }
  }

  // Add this method to sync payments with Stripe
  static Future<void> syncPaymentsWithStripe(int userId, String customerId) async {
    try {
      // Ensure user exists in database
      if (userId == 0 || userId == 999) {
        await _ensureTestUserExists(userId);
      }
      
      // Use the new StripeSyncService for better synchronization
      final syncService = StripeSyncService();
      await syncService.syncUserWithStripe(userId, customerId);
    } catch (e) {
      print('Error syncing payments with Stripe: $e');
    }
  }

  // Add this helper method to ensure test users exist in the database
  static Future<void> _ensureTestUserExists(int userId) async {
    try {
      // Check if user exists
      final users = await BackendApiService.executeQuery(
        'SELECT id FROM Users WHERE id = ?',
        [userId]
      );
      
      if (users.isEmpty) {
        // Create the test user if it doesn't exist
        await BackendApiService.executeQuery(
          'INSERT INTO Users (id, email, name) VALUES (?, ?, ?)',
          [userId, '<EMAIL>', 'Test User']
        );
        print('Created test user with ID: $userId');
      }
    } catch (e) {
      print('Error ensuring test user exists: $e');
    }
  }
}