# Stripe Platform Integration Guide

This guide explains how the Stripe service has been configured to work optimally with both iOS and Android platforms in the Immy App.

## Overview

The Stripe integration includes:
- ✅ Cross-platform payment processing
- ✅ Platform-specific UI/UX optimizations
- ✅ Apple Pay support (iOS)
- ✅ Google Pay support (Android)
- ✅ Platform-specific error handling
- ✅ Optimized payment sheet appearance
- ✅ ProGuard rules for Android
- ✅ URL scheme handling

## Platform Configurations

### iOS Configuration

**Info.plist additions:**
- URL schemes for Stripe redirects (`immy-app-stripe`)
- Apple Pay merchant identifier
- LSApplicationQueriesSchemes for Stripe
- Network security settings

**Key features:**
- Apple Pay integration
- iOS-specific payment sheet styling
- Touch ID/Face ID authentication support
- iOS-specific error messages

### Android Configuration

**AndroidManifest.xml additions:**
- Internet and network permissions
- NFC permissions for contactless payments
- URL scheme intent filters
- Stripe-specific permissions

**build.gradle enhancements:**
- Updated dependencies for Stripe support
- ProGuard rules to prevent obfuscation
- Minimum SDK 21 requirement
- MultiDex support

**Key features:**
- Google Pay integration
- Material Design payment sheet styling
- Fingerprint/PIN authentication support
- Android-specific error messages

## Services Architecture

### 1. StripeService (Enhanced)
- Platform detection methods
- Platform-specific payment method types
- Enhanced error handling with platform-specific messages
- Optimized payment intent creation

### 2. PlatformPaymentService (New)
- Apple Pay/Google Pay availability checks
- Platform-specific payment sheet initialization
- Customized UI appearance per platform
- Platform-specific payment method handling

### 3. PaymentProcessor (Updated)
- Uses platform-optimized payment intents
- Integrated with PlatformPaymentService
- Enhanced error handling with user-friendly messages
- Maintains backward compatibility

## Usage Examples

### Basic Payment Processing
```dart
final result = await PaymentProcessor.processPayment(
  userId: userId,
  serialId: serialId,
  amount: 7.99,
  currency: 'gbp',
  customerEmail: email,
  customerName: name,
);
```

### Platform-Specific Payment Methods
```dart
// Check available payment methods
final methods = await PlatformPaymentService.getAvailablePaymentMethods();

// Check Apple Pay availability (iOS)
final applePayAvailable = await PlatformPaymentService.isApplePayAvailable();

// Check Google Pay availability (Android)
final googlePayAvailable = await PlatformPaymentService.isGooglePayAvailable();
```

### Custom Payment Sheet
```dart
await PlatformPaymentService.initializePlatformPaymentSheet(
  paymentIntentClientSecret: clientSecret,
  merchantDisplayName: 'Immy App',
  customerId: customerId,
);

final result = await PlatformPaymentService.presentPaymentSheet();
```

## Error Handling

The system provides platform-specific error messages:

**iOS Examples:**
- "Please authenticate with Touch ID, Face ID, or your passcode to complete the payment."
- "Apple Pay is not available on this device. Please use a card instead."

**Android Examples:**
- "Please authenticate with your fingerprint, face unlock, or PIN to complete the payment."
- "Google Pay is not available on this device. Please use a card instead."

**Generic Examples:**
- "Your card was declined. Please try a different payment method."
- "Insufficient funds. Please check your account balance or try a different card."

## Testing

Run the platform-specific tests:
```bash
flutter test test/stripe_platform_test.dart
```

## Dependencies

Updated dependencies in `pubspec.yaml`:
- `flutter_stripe: ^11.1.0` (latest version)

## Security

### Android ProGuard Rules
The app includes ProGuard rules to ensure Stripe classes aren't obfuscated in release builds:
- Stripe SDK classes preserved
- JSON serialization maintained
- HTTP client classes protected

### iOS Security
- App Transport Security configured
- URL scheme validation
- Apple Pay merchant validation

## Deployment Checklist

### iOS
- [ ] Configure Apple Pay merchant identifier in Apple Developer Console
- [ ] Update URL schemes in Xcode project
- [ ] Test on physical iOS device
- [ ] Verify Apple Pay functionality

### Android
- [ ] Test ProGuard rules in release build
- [ ] Verify NFC permissions for contactless payments
- [ ] Test on physical Android device
- [ ] Verify Google Pay functionality

## Troubleshooting

### Common Issues

1. **Apple Pay not working:**
   - Verify merchant identifier is correctly configured
   - Check device supports Apple Pay
   - Ensure cards are added to Wallet

2. **Google Pay not working:**
   - Verify Google Play Services is updated
   - Check device supports NFC
   - Ensure Google Pay is set up

3. **Payment sheet not appearing:**
   - Check Stripe publishable key is valid
   - Verify network connectivity
   - Check for JavaScript errors (if using WebView)

4. **Android release build issues:**
   - Verify ProGuard rules are applied
   - Check for missing dependencies
   - Test with release build configuration

## Support

For additional support:
- Check Stripe documentation: https://stripe.com/docs/mobile
- Flutter Stripe plugin: https://pub.dev/packages/flutter_stripe
- Platform-specific guides in Stripe documentation
